"""Android icon generation functionality for HielToolbox."""

import os
from PIL import Image, ImageDraw
from pathlib import Path
from typing import Dict, List
from utils.file_handler import validate_image_format, validate_file_size
from core.config import ANDROID_ICON_SIZES


class AndroidIconGenerator:
    """Handle Android app icon generation."""

    @staticmethod
    def generate_icon_set(
        input_path: str,
        output_dir: str,
        app_name: str = "app"
    ) -> Dict:
        """
        Generate complete Android icon set from a single PNG.

        Args:
            input_path: Path to input PNG image
            output_dir: Base output directory
            app_name: Name for the app (used in folder structure)

        Returns:
            dict: Results with generated files and any errors
        """
        try:
            # Validate input
            if not validate_image_format(input_path):
                raise ValueError("Unsupported image format")

            if not validate_file_size(input_path):
                raise ValueError("File size too large")

            # Ensure input is PNG for best quality
            input_ext = Path(input_path).suffix.lower()
            if input_ext not in ['.png']:
                raise ValueError("Input must be PNG format for best results")

            results = {
                'success': [],
                'failed': [],
                'folders_created': []
            }

            # Open source image
            with Image.open(input_path) as source_img:
                # Convert to RGBA if not already
                if source_img.mode != 'RGBA':
                    source_img = source_img.convert('RGBA')

                # Generate icons for each density
                for density, size in ANDROID_ICON_SIZES.items():
                    try:
                        # Create density folder
                        density_folder = Path(output_dir) / f"mipmap-{density}"
                        density_folder.mkdir(parents=True, exist_ok=True)

                        if str(density_folder) not in results['folders_created']:
                            results['folders_created'].append(str(density_folder))

                        # Resize image
                        resized_img = source_img.resize((size, size), Image.Resampling.LANCZOS)

                        # Save icon
                        icon_path = density_folder / f"ic_launcher.png"
                        resized_img.save(icon_path, 'PNG', optimize=True)

                        results['success'].append({
                            'density': density,
                            'size': f"{size}x{size}",
                            'path': str(icon_path)
                        })

                        # Also create round icon variant
                        round_icon = AndroidIconGenerator._create_round_icon(resized_img, size)
                        round_icon_path = density_folder / f"ic_launcher_round.png"
                        round_icon.save(round_icon_path, 'PNG', optimize=True)

                        results['success'].append({
                            'density': density,
                            'size': f"{size}x{size}",
                            'path': str(round_icon_path),
                            'type': 'round'
                        })

                    except Exception as e:
                        results['failed'].append({
                            'density': density,
                            'error': str(e)
                        })

            # Generate adaptive icons (API 26+)
            AndroidIconGenerator._generate_adaptive_icons(
                input_path, output_dir, results
            )

            return results

        except Exception as e:
            return {
                'success': [],
                'failed': [{'error': str(e)}],
                'folders_created': []
            }

    @staticmethod
    def _create_round_icon(source_img: Image.Image, size: int) -> Image.Image:
        """Create a round version of the icon."""
        # Create a circular mask
        mask = Image.new('L', (size, size), 0)
        draw = ImageDraw.Draw(mask)
        draw.ellipse((0, 0, size, size), fill=255)

        # Apply mask to create round icon
        round_icon = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        round_icon.paste(source_img, (0, 0))
        round_icon.putalpha(mask)

        return round_icon

    @staticmethod
    def _generate_adaptive_icons(input_path: str, output_dir: str, results: Dict):
        """Generate adaptive icons for Android 8.0+ (API 26+)."""
        try:
            with Image.open(input_path) as source_img:
                if source_img.mode != 'RGBA':
                    source_img = source_img.convert('RGBA')

                # Adaptive icons need foreground and background layers
                # For simplicity, we'll create a foreground layer and transparent background

                for density, base_size in ANDROID_ICON_SIZES.items():
                    try:
                        # Adaptive icons are 108dp but safe area is 72dp
                        adaptive_size = int(base_size * 1.5)  # 108/72 ratio
                        safe_size = base_size

                        density_folder = Path(output_dir) / f"mipmap-{density}"

                        # Create foreground layer (centered in 108dp canvas)
                        foreground = Image.new('RGBA', (adaptive_size, adaptive_size), (0, 0, 0, 0))

                        # Resize source to safe area and center it
                        resized_source = source_img.resize((safe_size, safe_size), Image.Resampling.LANCZOS)
                        offset = (adaptive_size - safe_size) // 2
                        foreground.paste(resized_source, (offset, offset), resized_source)

                        # Save foreground
                        fg_path = density_folder / "ic_launcher_foreground.png"
                        foreground.save(fg_path, 'PNG', optimize=True)

                        # Create simple background (white)
                        background = Image.new('RGBA', (adaptive_size, adaptive_size), (255, 255, 255, 255))
                        bg_path = density_folder / "ic_launcher_background.png"
                        background.save(bg_path, 'PNG', optimize=True)

                        results['success'].extend([
                            {
                                'density': density,
                                'size': f"{adaptive_size}x{adaptive_size}",
                                'path': str(fg_path),
                                'type': 'adaptive_foreground'
                            },
                            {
                                'density': density,
                                'size': f"{adaptive_size}x{adaptive_size}",
                                'path': str(bg_path),
                                'type': 'adaptive_background'
                            }
                        ])

                    except Exception as e:
                        results['failed'].append({
                            'density': density,
                            'error': f"Adaptive icon error: {str(e)}",
                            'type': 'adaptive'
                        })

        except Exception as e:
            results['failed'].append({
                'error': f"Adaptive icons generation failed: {str(e)}",
                'type': 'adaptive'
            })

    @staticmethod
    def get_icon_info() -> Dict:
        """Get information about Android icon requirements."""
        return {
            'densities': ANDROID_ICON_SIZES,
            'formats': ['PNG (recommended)', 'WebP'],
            'adaptive_icon_size': '108dp (with 72dp safe area)',
            'requirements': [
                'Source image should be at least 512x512 pixels',
                'PNG format recommended for transparency',
                'Square aspect ratio required',
                'Avoid text or fine details that may not scale well'
            ]
        }