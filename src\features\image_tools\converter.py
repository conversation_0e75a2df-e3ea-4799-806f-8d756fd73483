"""Image format conversion functionality for HielToolbox."""

from PIL import Image
from pathlib import Path
from typing import List, Dict
from utils.file_handler import validate_image_format, validate_file_size, get_safe_filename


class ImageConverter:
    """Handle image format conversion operations."""

    SUPPORTED_FORMATS = {
        'JPEG': ['.jpg', '.jpeg'],
        'PNG': ['.png'],
        'ICO': ['.ico'],
        'BMP': ['.bmp'],
        'TIFF': ['.tiff', '.tif'],
        'WEBP': ['.webp']
    }

    @staticmethod
    def convert_image(
        input_path: str,
        output_path: str,
        quality: int = 95,
        optimize: bool = True
    ) -> bool:
        """
        Convert image from one format to another.

        Args:
            input_path: Path to input image
            output_path: Path for output image
            quality: JPEG quality (1-100)
            optimize: Whether to optimize the output

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate input
            if not validate_image_format(input_path):
                raise ValueError("Unsupported input image format")

            if not validate_file_size(input_path):
                raise ValueError("File size too large")

            # Determine output format
            output_ext = Path(output_path).suffix.lower()
            output_format = ImageConverter._get_format_from_extension(output_ext)

            if not output_format:
                raise ValueError(f"Unsupported output format: {output_ext}")

            # Open and convert image
            with Image.open(input_path) as img:
                # Handle different format requirements
                if output_format == 'JPEG':
                    # Convert to RGB for JPEG (no transparency)
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # Create white background for transparent images
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    img.save(output_path, 'JPEG', quality=quality, optimize=optimize)

                elif output_format == 'PNG':
                    # PNG supports transparency
                    if img.mode not in ('RGBA', 'RGB', 'L', 'LA'):
                        img = img.convert('RGBA')
                    img.save(output_path, 'PNG', optimize=optimize)

                elif output_format == 'ICO':
                    # ICO format for icons
                    # Convert to RGBA and resize if too large
                    if img.mode != 'RGBA':
                        img = img.convert('RGBA')

                    # ICO files work best with standard icon sizes
                    if img.width > 256 or img.height > 256:
                        img = img.resize((256, 256), Image.Resampling.LANCZOS)

                    img.save(output_path, 'ICO')

                elif output_format == 'WEBP':
                    # WebP supports both lossy and lossless
                    img.save(output_path, 'WEBP', quality=quality, optimize=optimize)

                else:
                    # For other formats (BMP, TIFF, etc.)
                    if output_format == 'BMP' and img.mode == 'RGBA':
                        # BMP doesn't support transparency
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        background.paste(img, mask=img.split()[-1])
                        img = background

                    img.save(output_path, output_format)

                return True

        except Exception as e:
            print(f"Error converting image: {e}")
            return False

    @staticmethod
    def batch_convert(
        input_files: List[str],
        output_dir: str,
        target_format: str,
        quality: int = 95,
        optimize: bool = True
    ) -> Dict:
        """
        Batch convert multiple images to target format.

        Args:
            input_files: List of input file paths
            output_dir: Output directory
            target_format: Target format extension (e.g., '.jpg', '.png')
            quality: JPEG quality (1-100)
            optimize: Whether to optimize output

        Returns:
            dict: Results with 'success' and 'failed' lists
        """
        results = {'success': [], 'failed': []}

        for input_file in input_files:
            try:
                input_path = Path(input_file)
                output_filename = input_path.stem + target_format.lower()
                output_path = get_safe_filename(output_filename, output_dir)

                success = ImageConverter.convert_image(
                    str(input_path),
                    output_path,
                    quality=quality,
                    optimize=optimize
                )

                if success:
                    results['success'].append({
                        'input': str(input_path),
                        'output': output_path
                    })
                else:
                    results['failed'].append(str(input_path))

            except Exception as e:
                results['failed'].append(str(input_file))
                print(f"Failed to convert {input_file}: {e}")

        return results

    @staticmethod
    def _get_format_from_extension(extension: str) -> str:
        """Get PIL format name from file extension."""
        extension = extension.lower()
        for format_name, extensions in ImageConverter.SUPPORTED_FORMATS.items():
            if extension in extensions:
                return format_name
        return None

    @staticmethod
    def get_supported_formats() -> Dict[str, List[str]]:
        """Get dictionary of supported formats and their extensions."""
        return ImageConverter.SUPPORTED_FORMATS.copy()