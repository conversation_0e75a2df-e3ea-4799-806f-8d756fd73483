[project]
name = "hiel-toolbox"
version = "0.1.0"
description = ""
readme = "README.md"
requires-python = ">=3.9"
authors = [
    { name = "Flet developer", email = "<EMAIL>" }
]
dependencies = [
  "flet==0.28.3",
  "Pillow>=10.0.0",
  "rembg>=2.0.0",
  "pdf2docx>=0.5.0",
  "pdfplumber>=0.9.0",
  "openpyxl>=3.1.0",
  "qrcode[pil]>=7.4.0",
  "colorthief>=0.2.0"
]

[tool.flet]
# org name in reverse domain name notation, e.g. "com.mycompany".
# Combined with project.name to build bundle ID for iOS and Android apps
org = "com.mycompany"

# project display name that is used as an app title on Android and iOS home screens,
# shown in window titles and about app dialogs on desktop.
product = "hiel-toolbox"

# company name to display in about app dialogs
company = "Flet"

# copyright text to display in about app dialogs
copyright = "Copyright (C) 2025 by Flet"

[tool.flet.app]
path = "src"

[tool.uv]
dev-dependencies = [
    "flet[all]==0.28.3",
]

[tool.poetry]
package-mode = false

[tool.poetry.group.dev.dependencies]
flet = {extras = ["all"], version = "0.28.3"}