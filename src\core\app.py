"""Main application class for HielToolbox."""

import flet as ft
from .config import *


class HielToolboxApp:
    """Main application class handling navigation and state management."""

    def __init__(self, page: ft.Page):
        self.page = page
        self.current_view = "home"
        self.setup_page()
        self.setup_navigation()

    def setup_page(self):
        """Configure the main page settings."""
        self.page.title = APP_NAME
        self.page.window.width = WINDOW_WIDTH
        self.page.window.height = WINDOW_HEIGHT
        self.page.window.min_width = MIN_WINDOW_WIDTH
        self.page.window.min_height = MIN_WINDOW_HEIGHT
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.bgcolor = BACKGROUND_COLOR
        self.page.padding = 0

    def setup_navigation(self):
        """Set up the main navigation structure."""
        # Create sidebar
        self.sidebar = ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Text(
                        APP_NAME,
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.WHITE
                    ),
                    bgcolor=PRIMARY_COLOR,
                    padding=20,
                    alignment=ft.alignment.center
                ),
                # Navigation items
                ft.Container(
                    content=ft.Column([
                        self.create_nav_item("Home", ft.Icons.HOME, "home"),
                        self.create_nav_item("Image Tools", ft.Icons.IMAGE, "image_tools"),
                        self.create_nav_item("Document Tools", ft.Icons.DESCRIPTION, "document_tools"),
                        self.create_nav_item("Utilities", ft.Icons.BUILD, "utilities"),
                    ], spacing=5),
                    padding=ft.padding.all(10)
                )
            ], spacing=0),
            width=250,
            bgcolor=SURFACE_COLOR,
            border=ft.border.only(right=ft.BorderSide(1, ft.Colors.OUTLINE))
        )

        # Create main content area
        self.content_area = ft.Container(
            content=self.get_home_view(),
            expand=True,
            padding=20,
            bgcolor=BACKGROUND_COLOR
        )

        # Main layout
        main_layout = ft.Row([
            self.sidebar,
            self.content_area
        ], spacing=0, expand=True)

        self.page.add(main_layout)

    def create_nav_item(self, title: str, icon: str, view_name: str):
        """Create a navigation item."""
        is_selected = self.current_view == view_name

        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=PRIMARY_COLOR if is_selected else SECONDARY_COLOR),
                ft.Text(
                    title,
                    color=PRIMARY_COLOR if is_selected else SECONDARY_COLOR,
                    weight=ft.FontWeight.BOLD if is_selected else ft.FontWeight.NORMAL
                )
            ], spacing=10),
            padding=ft.padding.symmetric(horizontal=15, vertical=10),
            bgcolor=ft.Colors.BLUE_50 if is_selected else None,
            border_radius=8,
            on_click=lambda e, view=view_name: self.navigate_to(view),
            ink=True
        )

    def navigate_to(self, view_name: str):
        """Navigate to a specific view."""
        self.current_view = view_name

        # Update content area
        if view_name == "home":
            self.content_area.content = self.get_home_view()
        elif view_name == "image_tools":
            self.content_area.content = self.get_image_tools_view()
        elif view_name == "document_tools":
            self.content_area.content = self.get_document_tools_view()
        elif view_name == "utilities":
            self.content_area.content = self.get_utilities_view()

        # Refresh navigation
        self.setup_navigation()
        self.page.update()

    def get_home_view(self):
        """Get the home view content."""
        return ft.Column([
            ft.Text(
                f"Welcome to {APP_NAME}",
                size=32,
                weight=ft.FontWeight.BOLD,
                color=PRIMARY_COLOR
            ),
            ft.Text(
                APP_DESCRIPTION,
                size=16,
                color=SECONDARY_COLOR
            ),
            ft.Divider(height=30),
            ft.Text(
                "Available Features:",
                size=20,
                weight=ft.FontWeight.BOLD
            ),
            ft.Column([
                self.create_feature_card(
                    "Image Tools",
                    "Resize, convert formats, remove backgrounds, generate Android icons",
                    ft.Icons.IMAGE,
                    "image_tools"
                ),
                self.create_feature_card(
                    "Document Tools",
                    "Convert PDFs to DOCX/XLSX, merge and split documents",
                    ft.Icons.DESCRIPTION,
                    "document_tools"
                ),
                self.create_feature_card(
                    "Utilities",
                    "File organizer, QR generator, color extractor, batch renamer",
                    ft.Icons.BUILD,
                    "utilities"
                )
            ], spacing=15)
        ], spacing=20)

    def create_feature_card(self, title: str, description: str, icon: str, view_name: str):
        """Create a feature card for the home view."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, size=40, color=PRIMARY_COLOR),
                ft.Column([
                    ft.Text(title, size=18, weight=ft.FontWeight.BOLD),
                    ft.Text(description, size=14, color=SECONDARY_COLOR)
                ], spacing=5, expand=True),
                ft.IconButton(
                    icon=ft.Icons.ARROW_FORWARD,
                    on_click=lambda e: self.navigate_to(view_name)
                )
            ], spacing=15),
            padding=20,
            bgcolor=SURFACE_COLOR,
            border_radius=10,
            border=ft.border.all(1, ft.Colors.OUTLINE),
            ink=True,
            on_click=lambda e: self.navigate_to(view_name)
        )

    def get_image_tools_view(self):
        """Get the image tools view content."""
        # File picker for input files
        self.image_file_picker = ft.FilePicker(
            on_result=self.on_image_files_selected
        )
        self.page.overlay.append(self.image_file_picker)

        # Selected files list
        self.selected_files_list = ft.Column([], spacing=5)

        # Output directory picker
        self.output_dir_picker = ft.FilePicker(
            on_result=self.on_output_dir_selected
        )
        self.page.overlay.append(self.output_dir_picker)

        # Output directory display
        self.output_dir_text = ft.Text("No output directory selected", color=SECONDARY_COLOR)

        # Tool selection
        self.tool_selection = ft.Dropdown(
            label="Select Tool",
            options=[
                ft.dropdown.Option("resize", "Image Resizer"),
                ft.dropdown.Option("convert", "Format Converter"),
                ft.dropdown.Option("bg_remove", "Background Remover"),
                ft.dropdown.Option("android_icons", "Android Icon Generator"),
            ],
            value="resize",
            on_change=self.on_tool_changed
        )

        # Tool-specific controls container
        self.tool_controls = ft.Container()

        # Progress bar
        self.progress_bar = ft.ProgressBar(visible=False)
        self.progress_text = ft.Text("", visible=False)

        # Results area
        self.results_area = ft.Container()

        # Initialize with resize controls
        self.update_tool_controls("resize")

        return ft.Column([
            ft.Text(
                "Image Tools",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=PRIMARY_COLOR
            ),
            ft.Text(
                "Process and manipulate images with various tools",
                size=16,
                color=SECONDARY_COLOR
            ),
            ft.Divider(height=20),

            # File selection section
            ft.Container(
                content=ft.Column([
                    ft.Text("1. Select Images", size=18, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.ElevatedButton(
                            "Select Images",
                            icon=ft.Icons.FOLDER_OPEN,
                            on_click=lambda _: self.image_file_picker.pick_files(
                                allow_multiple=True,
                                allowed_extensions=["jpg", "jpeg", "png", "bmp", "tiff", "webp"]
                            )
                        ),
                        ft.ElevatedButton(
                            "Clear Selection",
                            icon=ft.Icons.CLEAR,
                            on_click=self.clear_selected_files
                        )
                    ], spacing=10),
                    self.selected_files_list
                ], spacing=10),
                padding=20,
                bgcolor=SURFACE_COLOR,
                border_radius=10
            ),

            # Output directory section
            ft.Container(
                content=ft.Column([
                    ft.Text("2. Select Output Directory", size=18, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.ElevatedButton(
                            "Select Directory",
                            icon=ft.Icons.FOLDER,
                            on_click=lambda _: self.output_dir_picker.get_directory_path()
                        )
                    ]),
                    self.output_dir_text
                ], spacing=10),
                padding=20,
                bgcolor=SURFACE_COLOR,
                border_radius=10
            ),

            # Tool selection and controls
            ft.Container(
                content=ft.Column([
                    ft.Text("3. Configure Tool", size=18, weight=ft.FontWeight.BOLD),
                    self.tool_selection,
                    self.tool_controls
                ], spacing=10),
                padding=20,
                bgcolor=SURFACE_COLOR,
                border_radius=10
            ),

            # Process button
            ft.Container(
                content=ft.Column([
                    ft.ElevatedButton(
                        "Process Images",
                        icon=ft.Icons.PLAY_ARROW,
                        style=ft.ButtonStyle(
                            bgcolor=PRIMARY_COLOR,
                            color=ft.Colors.WHITE
                        ),
                        on_click=self.process_images
                    ),
                    self.progress_bar,
                    self.progress_text
                ], spacing=10),
                padding=20,
                bgcolor=SURFACE_COLOR,
                border_radius=10
            ),

            # Results
            self.results_area

        ], spacing=20, scroll=ft.ScrollMode.AUTO)

    def get_document_tools_view(self):
        """Get the document tools view content."""
        return ft.Column([
            ft.Text(
                "Document Tools",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=PRIMARY_COLOR
            ),
            ft.Text(
                "Convert and manipulate documents",
                size=16,
                color=SECONDARY_COLOR
            ),
            ft.Divider(height=20),
            # Placeholder for document tools
            ft.Container(
                content=ft.Text("Document tools will be implemented here"),
                padding=20,
                bgcolor=SURFACE_COLOR,
                border_radius=10
            )
        ], spacing=20)

    def get_utilities_view(self):
        """Get the utilities view content."""
        return ft.Column([
            ft.Text(
                "Utilities",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=PRIMARY_COLOR
            ),
            ft.Text(
                "Additional utility tools for productivity",
                size=16,
                color=SECONDARY_COLOR
            ),
            ft.Divider(height=20),
            # Placeholder for utilities
            ft.Container(
                content=ft.Text("Utility tools will be implemented here"),
                padding=20,
                bgcolor=SURFACE_COLOR,
                border_radius=10
            )
        ], spacing=20)

    # Image tools helper methods
    def on_image_files_selected(self, e: ft.FilePickerResultEvent):
        """Handle image file selection."""
        if e.files:
            self.selected_files_list.controls.clear()
            for file in e.files:
                self.selected_files_list.controls.append(
                    ft.Text(f"• {file.name}", size=12)
                )
            self.page.update()

    def clear_selected_files(self, e):
        """Clear selected files list."""
        self.selected_files_list.controls.clear()
        self.page.update()

    def on_output_dir_selected(self, e: ft.FilePickerResultEvent):
        """Handle output directory selection."""
        if e.path:
            self.output_dir_text.value = f"Output: {e.path}"
            self.output_dir_text.color = PRIMARY_COLOR
            self.page.update()

    def on_tool_changed(self, e):
        """Handle tool selection change."""
        self.update_tool_controls(e.control.value)
        self.page.update()

    def update_tool_controls(self, tool_type):
        """Update tool-specific controls based on selection."""
        if tool_type == "resize":
            self.tool_controls.content = self.get_resize_controls()
        elif tool_type == "convert":
            self.tool_controls.content = self.get_convert_controls()
        elif tool_type == "bg_remove":
            self.tool_controls.content = self.get_bg_remove_controls()
        elif tool_type == "android_icons":
            self.tool_controls.content = self.get_android_icons_controls()

    def get_resize_controls(self):
        """Get controls for image resizing."""
        return ft.Column([
            ft.Text("Resize Options:", weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.TextField(
                    label="Width (px)",
                    width=120,
                    keyboard_type=ft.KeyboardType.NUMBER,
                    data="width"
                ),
                ft.TextField(
                    label="Height (px)",
                    width=120,
                    keyboard_type=ft.KeyboardType.NUMBER,
                    data="height"
                ),
                ft.TextField(
                    label="Percentage",
                    width=120,
                    keyboard_type=ft.KeyboardType.NUMBER,
                    hint_text="e.g., 0.5 for 50%",
                    data="percentage"
                )
            ], spacing=10),
            ft.Row([
                ft.Checkbox(
                    label="Maintain aspect ratio",
                    value=True,
                    data="maintain_aspect"
                ),
                ft.Slider(
                    min=1,
                    max=100,
                    value=95,
                    label="Quality: {value}%",
                    data="quality"
                )
            ], spacing=20)
        ], spacing=10)

    def get_convert_controls(self):
        """Get controls for format conversion."""
        return ft.Column([
            ft.Text("Conversion Options:", weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Dropdown(
                    label="Target Format",
                    options=[
                        ft.dropdown.Option(".jpg", "JPEG"),
                        ft.dropdown.Option(".png", "PNG"),
                        ft.dropdown.Option(".ico", "ICO"),
                        ft.dropdown.Option(".bmp", "BMP"),
                        ft.dropdown.Option(".webp", "WebP")
                    ],
                    value=".jpg",
                    data="target_format"
                ),
                ft.Slider(
                    min=1,
                    max=100,
                    value=95,
                    label="Quality: {value}%",
                    data="quality"
                )
            ], spacing=20),
            ft.Checkbox(
                label="Optimize output",
                value=True,
                data="optimize"
            )
        ], spacing=10)

    def process_images(self, e):
        """Process selected images with chosen tool."""
        # Validate inputs
        if not self.selected_files_list.controls:
            self.show_error("Please select images to process")
            return

        if "No output directory selected" in self.output_dir_text.value:
            self.show_error("Please select an output directory")
            return

        # Show progress
        self.progress_bar.visible = True
        self.progress_text.visible = True
        self.progress_text.value = "Processing images..."
        self.page.update()

        try:
            # Get selected tool and parameters
            tool_type = self.tool_selection.value
            output_dir = self.output_dir_text.value.replace("Output: ", "")

            # Get file paths
            file_paths = []
            if hasattr(self.image_file_picker, 'result') and self.image_file_picker.result:
                file_paths = [f.path for f in self.image_file_picker.result.files]

            if tool_type == "resize":
                results = self.process_resize(file_paths, output_dir)
            elif tool_type == "convert":
                results = self.process_convert(file_paths, output_dir)
            elif tool_type == "bg_remove":
                results = self.process_bg_remove(file_paths, output_dir)
            elif tool_type == "android_icons":
                results = self.process_android_icons(file_paths, output_dir)

            self.show_results(results)

        except Exception as ex:
            self.show_error(f"Error processing images: {str(ex)}")
        finally:
            self.progress_bar.visible = False
            self.progress_text.visible = False
            self.page.update()

    def process_resize(self, file_paths, output_dir):
        """Process image resizing."""
        from features.image_tools.resizer import ImageResizer

        # Get resize parameters from controls
        width = None
        height = None
        percentage = None
        maintain_aspect = True
        quality = 95

        # Extract values from controls (simplified for now)
        # In a real implementation, you'd traverse the controls to get values

        return ImageResizer.batch_resize(
            file_paths,
            output_dir,
            width=width,
            height=height,
            percentage=percentage,
            maintain_aspect_ratio=maintain_aspect,
            quality=quality
        )

    def process_convert(self, file_paths, output_dir):
        """Process format conversion."""
        from features.image_tools.converter import ImageConverter

        # Get conversion parameters
        target_format = ".jpg"  # Default, should get from controls
        quality = 95
        optimize = True

        return ImageConverter.batch_convert(
            file_paths,
            output_dir,
            target_format,
            quality=quality,
            optimize=optimize
        )

    def process_bg_remove(self, file_paths, output_dir):
        """Process background removal."""
        from features.image_tools.bg_remover import BackgroundRemover

        # Get background removal parameters
        model_name = "u2net"  # Default, should get from controls

        return BackgroundRemover.batch_remove_background(
            file_paths,
            output_dir,
            model_name=model_name
        )

    def process_android_icons(self, file_paths, output_dir):
        """Process Android icon generation."""
        from features.image_tools.icon_generator import AndroidIconGenerator

        # Android icon generation works on single files
        results = {'success': [], 'failed': []}

        for file_path in file_paths:
            try:
                # Get app name from controls (simplified)
                app_name = "app"  # Should get from controls

                # Create subfolder for this icon set
                icon_output_dir = Path(output_dir) / f"android_icons_{Path(file_path).stem}"
                icon_output_dir.mkdir(parents=True, exist_ok=True)

                result = AndroidIconGenerator.generate_icon_set(
                    file_path,
                    str(icon_output_dir),
                    app_name=app_name
                )

                if result['success']:
                    results['success'].extend(result['success'])
                if result['failed']:
                    results['failed'].extend(result['failed'])

            except Exception as e:
                results['failed'].append({'error': f"Failed to process {file_path}: {str(e)}"})

        return results

    def show_results(self, results):
        """Display processing results."""
        success_count = len(results.get('success', []))
        failed_count = len(results.get('failed', []))

        result_text = f"Processing complete!\nSuccessful: {success_count}\nFailed: {failed_count}"

        self.results_area.content = ft.Container(
            content=ft.Column([
                ft.Text("Results:", size=18, weight=ft.FontWeight.BOLD),
                ft.Text(result_text, color=SUCCESS_COLOR if failed_count == 0 else ERROR_COLOR)
            ]),
            padding=20,
            bgcolor=SURFACE_COLOR,
            border_radius=10
        )
        self.page.update()

    def show_error(self, message):
        """Display error message."""
        self.results_area.content = ft.Container(
            content=ft.Column([
                ft.Text("Error:", size=18, weight=ft.FontWeight.BOLD, color=ERROR_COLOR),
                ft.Text(message, color=ERROR_COLOR)
            ]),
            padding=20,
            bgcolor=SURFACE_COLOR,
            border_radius=10
        )
        self.page.update()

    def get_bg_remove_controls(self):
        """Get controls for background removal."""
        from features.image_tools.bg_remover import BackgroundRemover

        if not BackgroundRemover.is_available():
            return ft.Container(
                content=ft.Column([
                    ft.Text("Background Removal Not Available",
                           weight=ft.FontWeight.BOLD,
                           color=ERROR_COLOR),
                    ft.Text("Please install the 'rembg' package to use this feature.",
                           color=SECONDARY_COLOR),
                    ft.Text("Run: pip install rembg",
                           color=SECONDARY_COLOR,
                           style=ft.TextStyle(font_family="monospace"))
                ]),
                padding=20,
                bgcolor=ft.Colors.RED_50,
                border_radius=10
            )

        models = BackgroundRemover.get_available_models()
        return ft.Column([
            ft.Text("Background Removal Options:", weight=ft.FontWeight.BOLD),
            ft.Dropdown(
                label="AI Model",
                options=[ft.dropdown.Option(model, model.replace('_', ' ').title()) for model in models],
                value=models[0] if models else "u2net",
                data="bg_model"
            ),
            ft.Text("Note: Output will be PNG format to preserve transparency",
                   size=12,
                   color=SECONDARY_COLOR)
        ], spacing=10)

    def get_android_icons_controls(self):
        """Get controls for Android icon generation."""
        return ft.Column([
            ft.Text("Android Icon Generation:", weight=ft.FontWeight.BOLD),
            ft.TextField(
                label="App Name (optional)",
                hint_text="Used for folder organization",
                data="app_name"
            ),
            ft.Text("Requirements:", weight=ft.FontWeight.BOLD, size=14),
            ft.Column([
                ft.Text("• Input must be PNG format", size=12),
                ft.Text("• Recommended size: 512x512 pixels or larger", size=12),
                ft.Text("• Square aspect ratio required", size=12),
                ft.Text("• Will generate all Android density folders", size=12)
            ], spacing=2),
            ft.Text("Generated files:", weight=ft.FontWeight.BOLD, size=14),
            ft.Column([
                ft.Text("• Standard icons (ic_launcher.png)", size=12),
                ft.Text("• Round icons (ic_launcher_round.png)", size=12),
                ft.Text("• Adaptive icons (foreground/background)", size=12),
                ft.Text("• All density folders (mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi)", size=12)
            ], spacing=2)
        ], spacing=10)