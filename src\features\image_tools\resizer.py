"""Image resizing functionality for HielToolbox."""

from PIL import Image, ImageOps
from pathlib import Path
from typing import Tuple, Optional
from utils.file_handler import validate_image_format, validate_file_size, get_safe_filename


class ImageResizer:
    """Handle image resizing operations."""

    @staticmethod
    def resize_image(
        input_path: str,
        output_path: str,
        width: Optional[int] = None,
        height: Optional[int] = None,
        percentage: Optional[float] = None,
        maintain_aspect_ratio: bool = True,
        quality: int = 95
    ) -> bool:
        """
        Resize an image with various options.

        Args:
            input_path: Path to input image
            output_path: Path for output image
            width: Target width in pixels
            height: Target height in pixels
            percentage: Resize by percentage (0.1 to 5.0)
            maintain_aspect_ratio: Whether to maintain aspect ratio
            quality: JPEG quality (1-100)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate input
            if not validate_image_format(input_path):
                raise ValueError("Unsupported image format")

            if not validate_file_size(input_path):
                raise ValueError("File size too large")

            # Open image
            with Image.open(input_path) as img:
                # Convert to RGB if necessary (for JPEG output)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                original_width, original_height = img.size

                # Calculate new dimensions
                if percentage:
                    new_width = int(original_width * percentage)
                    new_height = int(original_height * percentage)
                elif width and height:
                    if maintain_aspect_ratio:
                        # Calculate aspect ratio preserving dimensions
                        aspect_ratio = original_width / original_height
                        if width / height > aspect_ratio:
                            new_width = int(height * aspect_ratio)
                            new_height = height
                        else:
                            new_width = width
                            new_height = int(width / aspect_ratio)
                    else:
                        new_width, new_height = width, height
                elif width:
                    aspect_ratio = original_width / original_height
                    new_width = width
                    new_height = int(width / aspect_ratio)
                elif height:
                    aspect_ratio = original_width / original_height
                    new_height = height
                    new_width = int(height * aspect_ratio)
                else:
                    raise ValueError("Must specify width, height, or percentage")

                # Resize image
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Save with appropriate format and quality
                output_format = Path(output_path).suffix.lower()
                if output_format in ['.jpg', '.jpeg']:
                    resized_img.save(output_path, 'JPEG', quality=quality, optimize=True)
                elif output_format == '.png':
                    resized_img.save(output_path, 'PNG', optimize=True)
                else:
                    resized_img.save(output_path)

                return True

        except Exception as e:
            print(f"Error resizing image: {e}")
            return False

    @staticmethod
    def batch_resize(
        input_files: list,
        output_dir: str,
        width: Optional[int] = None,
        height: Optional[int] = None,
        percentage: Optional[float] = None,
        maintain_aspect_ratio: bool = True,
        quality: int = 95
    ) -> dict:
        """
        Batch resize multiple images.

        Returns:
            dict: Results with 'success' and 'failed' lists
        """
        results = {'success': [], 'failed': []}

        for input_file in input_files:
            try:
                input_path = Path(input_file)
                output_filename = input_path.name
                output_path = get_safe_filename(output_filename, output_dir)

                success = ImageResizer.resize_image(
                    str(input_path),
                    output_path,
                    width=width,
                    height=height,
                    percentage=percentage,
                    maintain_aspect_ratio=maintain_aspect_ratio,
                    quality=quality
                )

                if success:
                    results['success'].append({
                        'input': str(input_path),
                        'output': output_path
                    })
                else:
                    results['failed'].append(str(input_path))

            except Exception as e:
                results['failed'].append(str(input_file))
                print(f"Failed to process {input_file}: {e}")

        return results

    @staticmethod
    def get_image_info(image_path: str) -> dict:
        """Get basic information about an image."""
        try:
            with Image.open(image_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'size_mb': Path(image_path).stat().st_size / (1024 * 1024)
                }
        except Exception as e:
            return {'error': str(e)}