"""Background removal functionality for HielToolbox."""

from PIL import Image
from pathlib import Path
from typing import List, Dict
from utils.file_handler import validate_image_format, validate_file_size, get_safe_filename

try:
    from rembg import remove
    REMBG_AVAILABLE = True
except ImportError:
    REMBG_AVAILABLE = False


class BackgroundRemover:
    """Handle background removal operations."""

    @staticmethod
    def is_available() -> bool:
        """Check if background removal is available."""
        return REMBG_AVAILABLE

    @staticmethod
    def remove_background(
        input_path: str,
        output_path: str,
        model_name: str = 'u2net'
    ) -> bool:
        """
        Remove background from an image.

        Args:
            input_path: Path to input image
            output_path: Path for output image
            model_name: Model to use for background removal

        Returns:
            bool: True if successful, False otherwise
        """
        if not REMBG_AVAILABLE:
            print("Background removal not available. Install rembg package.")
            return False

        try:
            # Validate input
            if not validate_image_format(input_path):
                raise ValueError("Unsupported image format")

            if not validate_file_size(input_path):
                raise ValueError("File size too large")

            # Open and process image
            with open(input_path, 'rb') as input_file:
                input_data = input_file.read()

            # Remove background
            output_data = remove(input_data, model_name=model_name)

            # Save result
            with open(output_path, 'wb') as output_file:
                output_file.write(output_data)

            return True

        except Exception as e:
            print(f"Error removing background: {e}")
            return False

    @staticmethod
    def batch_remove_background(
        input_files: List[str],
        output_dir: str,
        model_name: str = 'u2net'
    ) -> Dict:
        """
        Batch remove backgrounds from multiple images.

        Args:
            input_files: List of input file paths
            output_dir: Output directory
            model_name: Model to use for background removal

        Returns:
            dict: Results with 'success' and 'failed' lists
        """
        if not REMBG_AVAILABLE:
            return {'success': [], 'failed': input_files}

        results = {'success': [], 'failed': []}

        for input_file in input_files:
            try:
                input_path = Path(input_file)
                # Always output as PNG to preserve transparency
                output_filename = input_path.stem + "_no_bg.png"
                output_path = get_safe_filename(output_filename, output_dir)

                success = BackgroundRemover.remove_background(
                    str(input_path),
                    output_path,
                    model_name=model_name
                )

                if success:
                    results['success'].append({
                        'input': str(input_path),
                        'output': output_path
                    })
                else:
                    results['failed'].append(str(input_path))

            except Exception as e:
                results['failed'].append(str(input_file))
                print(f"Failed to process {input_file}: {e}")

        return results

    @staticmethod
    def get_available_models() -> List[str]:
        """Get list of available background removal models."""
        if not REMBG_AVAILABLE:
            return []

        # Common rembg models
        return [
            'u2net',           # General use
            'u2netp',          # Lightweight version
            'u2net_human_seg', # Human segmentation
            'silueta',         # General use
            'isnet-general-use' # High quality
        ]