"""Tests for image tools functionality."""

import unittest
import tempfile
import os
from pathlib import Path
from PIL import Image
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from features.image_tools.resizer import ImageResizer
from features.image_tools.converter import ImageConverter


class TestImageResizer(unittest.TestCase):
    """Test cases for ImageResizer class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()

        # Create a test image
        self.test_image_path = os.path.join(self.temp_dir, "test_image.png")
        test_img = Image.new('RGB', (100, 100), color='red')
        test_img.save(self.test_image_path)

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_resize_by_percentage(self):
        """Test resizing image by percentage."""
        output_path = os.path.join(self.temp_dir, "resized_percentage.png")

        result = ImageResizer.resize_image(
            self.test_image_path,
            output_path,
            percentage=0.5
        )

        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_path))

        # Check dimensions
        with Image.open(output_path) as img:
            self.assertEqual(img.size, (50, 50))

    def test_resize_by_dimensions(self):
        """Test resizing image by specific dimensions."""
        output_path = os.path.join(self.temp_dir, "resized_dimensions.png")

        result = ImageResizer.resize_image(
            self.test_image_path,
            output_path,
            width=200,
            height=150
        )

        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_path))

        # Check dimensions (should maintain aspect ratio by default)
        with Image.open(output_path) as img:
            self.assertEqual(img.width, 150)  # Adjusted to maintain aspect ratio
            self.assertEqual(img.height, 150)

    def test_get_image_info(self):
        """Test getting image information."""
        info = ImageResizer.get_image_info(self.test_image_path)

        self.assertIn('width', info)
        self.assertIn('height', info)
        self.assertEqual(info['width'], 100)
        self.assertEqual(info['height'], 100)


class TestImageConverter(unittest.TestCase):
    """Test cases for ImageConverter class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()

        # Create a test image
        self.test_image_path = os.path.join(self.temp_dir, "test_image.png")
        test_img = Image.new('RGB', (50, 50), color='blue')
        test_img.save(self.test_image_path)

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_convert_png_to_jpg(self):
        """Test converting PNG to JPG."""
        output_path = os.path.join(self.temp_dir, "converted.jpg")

        result = ImageConverter.convert_image(
            self.test_image_path,
            output_path
        )

        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_path))

        # Check format
        with Image.open(output_path) as img:
            self.assertEqual(img.format, 'JPEG')

    def test_get_supported_formats(self):
        """Test getting supported formats."""
        formats = ImageConverter.get_supported_formats()

        self.assertIn('JPEG', formats)
        self.assertIn('PNG', formats)
        self.assertIn('ICO', formats)


if __name__ == '__main__':
    unittest.main()