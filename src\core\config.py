"""Configuration settings for HielToolbox application."""

import os
from pathlib import Path

# Application settings
APP_NAME = "HielToolbox"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "A lightweight desktop app for daily productivity tasks"

# UI Configuration
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 800
MIN_WINDOW_HEIGHT = 600

# Theme colors (using Flet 0.28+ color system)
PRIMARY_COLOR = "#1976D2"  # Blue
SECONDARY_COLOR = "#424242"  # Dark Grey
ACCENT_COLOR = "#FF5722"  # Deep Orange
BACKGROUND_COLOR = "#FAFAFA"  # Light Grey
SURFACE_COLOR = "#FFFFFF"  # White
ERROR_COLOR = "#F44336"  # Red
SUCCESS_COLOR = "#4CAF50"  # Green

# File handling
SUPPORTED_IMAGE_FORMATS = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]
SUPPORTED_DOCUMENT_FORMATS = [".pdf", ".docx", ".xlsx"]
MAX_FILE_SIZE_MB = 100

# Storage paths
BASE_DIR = Path(__file__).parent.parent.parent
STORAGE_DIR = BASE_DIR / "storage"
TEMP_DIR = STORAGE_DIR / "temp"
DATA_DIR = STORAGE_DIR / "data"

# Ensure storage directories exist
STORAGE_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)

# Android icon sizes for generation
ANDROID_ICON_SIZES = {
    "mdpi": 48,
    "hdpi": 72,
    "xhdpi": 96,
    "xxhdpi": 144,
    "xxxhdpi": 192
}