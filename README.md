# HielToolbox - Desktop Productivity App

A lightweight, feature-based desktop application built with Flet 0.28+ for daily productivity tasks. HielToolbox provides a clean, enterprise-grade UI with modular feature implementation for image processing, document conversion, and utility tools.

## Features

### 🖼️ Image Processing Tools
- **Image Resizer**: Batch resize images with custom dimensions, percentage scaling, and aspect ratio preservation
- **Background Remover**: AI-powered background removal using rembg models
- **Format Converter**: Convert between JPG, PNG, ICO, BMP, TIFF, and WebP formats
- **Android Icon Generator**: Generate complete Android app icon sets from a single PNG with proper folder structure

### 📄 Document Processing Tools
- **PDF to DOCX Converter**: Convert PDF documents to editable Word format
- **PDF to XLSX Converter**: Extract tables from PDFs to Excel format with intelligent table detection

### 🛠️ Utility Tools
- **QR Code Generator**: Generate QR codes with customizable styles and error correction
- **File Organizer**: Organize files by type, date, or custom rules
- **Batch File Renamer**: Rename multiple files with patterns
- **Color Palette Extractor**: Extract color palettes from images

## Installation

### Prerequisites
- Python 3.9 or higher
- pip or uv package manager

### Install Dependencies

Using pip:
```bash
pip install -r requirements.txt
```

Using uv:
```bash
uv sync
```

Using Poetry:
```bash
poetry install
```

### Optional Dependencies
For full functionality, install these optional packages:
```bash
pip install rembg  # For background removal
pip install pdf2docx pdfplumber openpyxl  # For PDF conversion
pip install qrcode[pil] colorthief  # For QR codes and color extraction
```

## Running the Application

### Desktop App

Using uv:
```bash
uv run flet run
```

Using Poetry:
```bash
poetry run flet run
```

Using Python directly:
```bash
python src/main.py
```

### Web App

```bash
uv run flet run --web
# or
poetry run flet run --web
```

## Usage Guide

### Image Tools

#### Image Resizer
1. Navigate to "Image Tools" in the sidebar
2. Select "Image Resizer" from the tool dropdown
3. Click "Select Images" to choose one or more image files
4. Choose an output directory
5. Configure resize options:
   - **By Dimensions**: Set specific width and height
   - **By Percentage**: Scale by percentage (e.g., 0.5 for 50%)
   - **Maintain Aspect Ratio**: Keep original proportions
   - **Quality**: JPEG quality setting (1-100)
6. Click "Process Images"

#### Format Converter
1. Select "Format Converter" from the tool dropdown
2. Choose target format (JPG, PNG, ICO, BMP, WebP)
3. Set quality and optimization options
4. Process your images

#### Background Remover
1. Select "Background Remover" from the tool dropdown
2. Choose AI model (u2net, u2netp, etc.)
3. Note: Requires `rembg` package installation
4. Output will be PNG format to preserve transparency

#### Android Icon Generator
1. Select "Android Icon Generator" from the tool dropdown
2. Choose a high-resolution PNG file (512x512 recommended)
3. Optionally set an app name
4. Generates complete icon sets for all Android densities

### Document Tools

#### PDF to DOCX
1. Navigate to "Document Tools"
2. Select PDF files for conversion
3. Choose output directory
4. Process to get editable Word documents

#### PDF to XLSX
1. Select PDF files containing tables
2. Choose between table extraction or full text extraction
3. Output Excel files with data organized by pages

### Testing

Run the test suite to verify functionality:

```bash
python tests/test_image_tools.py
```

## Project Structure

```
src/
├── main.py                 # Application entry point
├── core/
│   ├── app.py             # Main app class and navigation
│   └── config.py          # Configuration settings
├── features/
│   ├── image_tools/       # Image processing features
│   ├── document_tools/    # Document conversion features
│   └── utilities/         # Utility tools
├── ui/
│   ├── components/        # Reusable UI components
│   └── pages/            # Page-specific UI
└── utils/
    └── file_handler.py    # File handling utilities
```

## Configuration

The app uses configuration settings in `src/core/config.py`:

- **Window dimensions**: 1200x800 (minimum 800x600)
- **Theme colors**: Material Design inspired
- **File size limits**: 100MB maximum
- **Supported formats**: Defined per feature
- **Storage paths**: Automatic temp and data directories

## Dependencies

### Core Dependencies
- `flet==0.28.3` - UI framework
- `Pillow>=10.0.0` - Image processing

### Optional Dependencies
- `rembg>=2.0.0` - Background removal
- `pdf2docx>=0.5.0` - PDF to DOCX conversion
- `pdfplumber>=0.9.0` - PDF text/table extraction
- `openpyxl>=3.1.0` - Excel file handling
- `qrcode[pil]>=7.4.0` - QR code generation
- `colorthief>=0.2.0` - Color extraction

## Troubleshooting

### Common Issues

**Background removal not working:**
- Install rembg: `pip install rembg`
- First run may take time to download AI models

**PDF conversion failing:**
- Install required packages: `pip install pdf2docx pdfplumber openpyxl`
- Ensure PDF files are not password-protected

**Large file processing slow:**
- Files are limited to 100MB by default
- Consider resizing large images before processing

**App won't start:**
- Verify Python 3.9+ is installed
- Check all dependencies are installed
- Try running with `python src/main.py` directly

### Performance Tips

- Use smaller images for faster processing
- Enable optimization for better file sizes
- Process files in smaller batches for large operations
- Close the app between large batch operations to free memory

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Flet](https://flet.dev/) - Python framework for building native apps
- Uses [Pillow](https://pillow.readthedocs.io/) for image processing
- Background removal powered by [rembg](https://github.com/danielgatis/rembg)
- PDF processing with [pdf2docx](https://github.com/dothinking/pdf2docx) and [pdfplumber](https://github.com/jsvine/pdfplumber)

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review existing issues in the repository
3. Create a new issue with detailed information about your problem

---

**HielToolbox** - Making daily productivity tasks simple and efficient.

## Build the app

### Android

```
flet build apk -v
```

For more details on building and signing `.apk` or `.aab`, refer to the [Android Packaging Guide](https://flet.dev/docs/publish/android/).

### iOS

```
flet build ipa -v
```

For more details on building and signing `.ipa`, refer to the [iOS Packaging Guide](https://flet.dev/docs/publish/ios/).

### macOS

```
flet build macos -v
```

For more details on building macOS package, refer to the [macOS Packaging Guide](https://flet.dev/docs/publish/macos/).

### Linux

```
flet build linux -v
```

For more details on building Linux package, refer to the [Linux Packaging Guide](https://flet.dev/docs/publish/linux/).

### Windows

```
flet build windows -v
```

For more details on building Windows package, refer to the [Windows Packaging Guide](https://flet.dev/docs/publish/windows/).