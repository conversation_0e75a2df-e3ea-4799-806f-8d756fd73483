"""PDF conversion functionality for HielToolbox."""

import os
from pathlib import Path
from typing import List, Dict, Optional

try:
    from pdf2docx import Converter
    PDF2DOCX_AVAILABLE = True
except ImportError:
    PDF2DOCX_AVAILABLE = False

try:
    import pdfplumber
    import openpyxl
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False


class PDFConverter:
    """Handle PDF conversion operations."""

    @staticmethod
    def is_pdf2docx_available() -> bool:
        """Check if PDF to DOCX conversion is available."""
        return PDF2DOCX_AVAILABLE

    @staticmethod
    def is_pdf2xlsx_available() -> bool:
        """Check if PDF to XLSX conversion is available."""
        return PDFPLUMBER_AVAILABLE

    @staticmethod
    def convert_pdf_to_docx(
        input_path: str,
        output_path: str,
        start_page: int = 0,
        end_page: Optional[int] = None
    ) -> bool:
        """
        Convert PDF to DOCX format.

        Args:
            input_path: Path to input PDF
            output_path: Path for output DOCX
            start_page: Starting page (0-indexed)
            end_page: Ending page (None for all pages)

        Returns:
            bool: True if successful, False otherwise
        """
        if not PDF2DOCX_AVAILABLE:
            print("PDF to DOCX conversion not available. Install pdf2docx package.")
            return False

        try:
            # Validate input
            if not Path(input_path).exists():
                raise FileNotFoundError(f"Input file not found: {input_path}")

            if not input_path.lower().endswith('.pdf'):
                raise ValueError("Input must be a PDF file")

            # Convert PDF to DOCX
            cv = Converter(input_path)
            cv.convert(output_path, start=start_page, end=end_page)
            cv.close()

            return True

        except Exception as e:
            print(f"Error converting PDF to DOCX: {e}")
            return False

    @staticmethod
    def convert_pdf_to_xlsx(
        input_path: str,
        output_path: str,
        extract_tables_only: bool = True
    ) -> bool:
        """
        Convert PDF to XLSX format by extracting tables.

        Args:
            input_path: Path to input PDF
            output_path: Path for output XLSX
            extract_tables_only: If True, extract only tables; if False, extract all text

        Returns:
            bool: True if successful, False otherwise
        """
        if not PDFPLUMBER_AVAILABLE:
            print("PDF to XLSX conversion not available. Install pdfplumber and openpyxl packages.")
            return False

        try:
            # Validate input
            if not Path(input_path).exists():
                raise FileNotFoundError(f"Input file not found: {input_path}")

            if not input_path.lower().endswith('.pdf'):
                raise ValueError("Input must be a PDF file")

            # Create workbook
            workbook = openpyxl.Workbook()
            workbook.remove(workbook.active)  # Remove default sheet

            with pdfplumber.open(input_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    worksheet = workbook.create_sheet(title=f"Page_{page_num}")

                    if extract_tables_only:
                        # Extract tables
                        tables = page.extract_tables()
                        if tables:
                            row_offset = 1
                            for table_num, table in enumerate(tables):
                                # Add table header if multiple tables
                                if len(tables) > 1:
                                    worksheet.cell(row=row_offset, column=1, value=f"Table {table_num + 1}")
                                    row_offset += 1

                                # Add table data
                                for row_num, row in enumerate(table):
                                    for col_num, cell_value in enumerate(row):
                                        if cell_value:
                                            worksheet.cell(
                                                row=row_offset + row_num,
                                                column=col_num + 1,
                                                value=str(cell_value).strip()
                                            )

                                row_offset += len(table) + 2  # Add spacing between tables
                        else:
                            # No tables found, add a note
                            worksheet.cell(row=1, column=1, value="No tables found on this page")
                    else:
                        # Extract all text
                        text = page.extract_text()
                        if text:
                            lines = text.split('\n')
                            for row_num, line in enumerate(lines, 1):
                                worksheet.cell(row=row_num, column=1, value=line.strip())
                        else:
                            worksheet.cell(row=1, column=1, value="No text found on this page")

            # Save workbook
            workbook.save(output_path)
            return True

        except Exception as e:
            print(f"Error converting PDF to XLSX: {e}")
            return False

    @staticmethod
    def batch_convert_pdf_to_docx(
        input_files: List[str],
        output_dir: str
    ) -> Dict:
        """
        Batch convert multiple PDFs to DOCX format.

        Args:
            input_files: List of input PDF file paths
            output_dir: Output directory

        Returns:
            dict: Results with 'success' and 'failed' lists
        """
        results = {'success': [], 'failed': []}

        for input_file in input_files:
            try:
                input_path = Path(input_file)
                output_filename = input_path.stem + ".docx"
                output_path = Path(output_dir) / output_filename

                # Ensure unique filename
                counter = 1
                while output_path.exists():
                    output_filename = f"{input_path.stem}_{counter}.docx"
                    output_path = Path(output_dir) / output_filename
                    counter += 1

                success = PDFConverter.convert_pdf_to_docx(
                    str(input_path),
                    str(output_path)
                )

                if success:
                    results['success'].append({
                        'input': str(input_path),
                        'output': str(output_path)
                    })
                else:
                    results['failed'].append(str(input_path))

            except Exception as e:
                results['failed'].append(str(input_file))
                print(f"Failed to convert {input_file}: {e}")

        return results

    @staticmethod
    def batch_convert_pdf_to_xlsx(
        input_files: List[str],
        output_dir: str,
        extract_tables_only: bool = True
    ) -> Dict:
        """
        Batch convert multiple PDFs to XLSX format.

        Args:
            input_files: List of input PDF file paths
            output_dir: Output directory
            extract_tables_only: If True, extract only tables

        Returns:
            dict: Results with 'success' and 'failed' lists
        """
        results = {'success': [], 'failed': []}

        for input_file in input_files:
            try:
                input_path = Path(input_file)
                output_filename = input_path.stem + ".xlsx"
                output_path = Path(output_dir) / output_filename

                # Ensure unique filename
                counter = 1
                while output_path.exists():
                    output_filename = f"{input_path.stem}_{counter}.xlsx"
                    output_path = Path(output_dir) / output_filename
                    counter += 1

                success = PDFConverter.convert_pdf_to_xlsx(
                    str(input_path),
                    str(output_path),
                    extract_tables_only=extract_tables_only
                )

                if success:
                    results['success'].append({
                        'input': str(input_path),
                        'output': str(output_path)
                    })
                else:
                    results['failed'].append(str(input_path))

            except Exception as e:
                results['failed'].append(str(input_file))
                print(f"Failed to convert {input_file}: {e}")

        return results