# HielToolbox - Desktop App Development Plan

## Project Overview
A lightweight, feature-based desktop application built with Flet 0.28+ for daily productivity tasks. The app will have a clean, enterprise-grade UI with modular feature implementation.

## Core Features

### 1. Image Processing Tools
- **Image Resizer**: Batch resize images with custom dimensions, percentage scaling, and aspect ratio preservation
- **Background Remover**: AI-powered background removal for images
- **Format Converter**: Convert between JPG, PNG, ICO formats
- **Android Icon Generator**: Generate complete Android app icon sets from a single PNG with proper folder structure

### 2. Document Processing Tools
- **PDF to DOCX Converter**: Convert PDF documents to editable Word format
- **PDF to XLSX Converter**: Extract tables from PDFs to Excel format
- **Document Merger**: Combine multiple PDFs into one
- **Document Splitter**: Split PDFs into individual pages

### 3. Additional Utility Features
- **File Organizer**: Organize files by type, date, or custom rules
- **Batch File Renamer**: Rename multiple files with patterns
- **QR Code Generator**: Generate QR codes for text, URLs, etc.
- **Color Palette Extractor**: Extract color palettes from images

## Technical Architecture

### Project Structure
```
src/
├── main.py                 # Main application entry point
├── core/
│   ├── __init__.py
│   ├── app.py             # Main app class and navigation
│   └── config.py          # App configuration and settings
├── features/
│   ├── __init__.py
│   ├── image_tools/
│   │   ├── __init__.py
│   │   ├── resizer.py
│   │   ├── bg_remover.py
│   │   ├── converter.py
│   │   └── icon_generator.py
│   ├── document_tools/
│   │   ├── __init__.py
│   │   ├── pdf_converter.py
│   │   └── document_utils.py
│   └── utilities/
│       ├── __init__.py
│       ├── file_organizer.py
│       ├── qr_generator.py
│       └── color_extractor.py
├── ui/
│   ├── __init__.py
│   ├── components/
│   │   ├── __init__.py
│   │   ├── sidebar.py
│   │   ├── header.py
│   │   └── common.py
│   └── pages/
│       ├── __init__.py
│       ├── home.py
│       ├── image_tools.py
│       ├── document_tools.py
│       └── utilities.py
└── utils/
    ├── __init__.py
    ├── file_handler.py
    └── validators.py
```

### Dependencies Required
- `flet==0.28.3` (already included)
- `Pillow` - Image processing
- `rembg` - Background removal
- `pdf2docx` - PDF to DOCX conversion
- `pdfplumber` - PDF text/table extraction
- `openpyxl` - Excel file handling
- `qrcode` - QR code generation
- `colorthief` - Color extraction

### UI Design Principles
- Clean, modern interface with enterprise aesthetics
- Sidebar navigation for feature categories
- Consistent color scheme and typography
- Progress indicators for long-running operations
- Drag-and-drop file support
- Batch processing capabilities

## Implementation Phases

### Phase 1: Core Infrastructure
1. Set up main app structure with navigation
2. Create sidebar and header components
3. Implement file handling utilities
4. Set up basic routing between features

### Phase 2: Image Processing Features
1. Image Resizer with batch processing
2. Background Remover integration
3. Format Converter (JPG/PNG/ICO)
4. Android Icon Generator with folder structure

### Phase 3: Document Processing Features
1. PDF to DOCX converter
2. PDF to XLSX converter with table extraction
3. Document merger and splitter utilities

### Phase 4: Additional Utilities
1. File organizer with custom rules
2. Batch file renamer
3. QR code generator
4. Color palette extractor

### Phase 5: Testing and Polish
1. Unit tests for all features
2. Error handling and validation
3. Performance optimization
4. Documentation and user guide

## Key Flet 0.28+ Considerations
- Use `ft.Icons` instead of `icons`
- Use `ft.Colors` instead of `colors`
- Leverage new navigation and routing features
- Implement proper state management
- Use modern Flet components and layouts

## Success Criteria
- All core features working reliably
- Clean, intuitive user interface
- Efficient batch processing
- Proper error handling
- Comprehensive test coverage
- Complete documentation