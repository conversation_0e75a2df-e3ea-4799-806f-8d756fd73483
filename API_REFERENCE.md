# HielToolbox API Reference

## Core Classes

### ImageResizer

Located in `src/features/image_tools/resizer.py`

#### Methods

##### `resize_image(input_path, output_path, width=None, height=None, percentage=None, maintain_aspect_ratio=True, quality=95)`

Resize a single image.

**Parameters:**
- `input_path` (str): Path to input image
- `output_path` (str): Path for output image
- `width` (int, optional): Target width in pixels
- `height` (int, optional): Target height in pixels
- `percentage` (float, optional): Resize by percentage (0.1 to 5.0)
- `maintain_aspect_ratio` (bool): Whether to maintain aspect ratio
- `quality` (int): JPEG quality (1-100)

**Returns:** `bool` - True if successful

##### `batch_resize(input_files, output_dir, **kwargs)`

Resize multiple images.

**Parameters:**
- `input_files` (list): List of input file paths
- `output_dir` (str): Output directory
- `**kwargs`: Same parameters as `resize_image`

**Returns:** `dict` - Results with 'success' and 'failed' lists

##### `get_image_info(image_path)`

Get image information.

**Returns:** `dict` - Image dimensions, format, mode, and size

### ImageConverter

Located in `src/features/image_tools/converter.py`

#### Methods

##### `convert_image(input_path, output_path, quality=95, optimize=True)`

Convert image format.

**Parameters:**
- `input_path` (str): Path to input image
- `output_path` (str): Path for output image
- `quality` (int): JPEG quality (1-100)
- `optimize` (bool): Whether to optimize output

**Returns:** `bool` - True if successful

##### `batch_convert(input_files, output_dir, target_format, quality=95, optimize=True)`

Convert multiple images.

**Parameters:**
- `input_files` (list): List of input file paths
- `output_dir` (str): Output directory
- `target_format` (str): Target format extension (e.g., '.jpg')
- `quality` (int): JPEG quality
- `optimize` (bool): Whether to optimize

**Returns:** `dict` - Results with 'success' and 'failed' lists

##### `get_supported_formats()`

Get supported image formats.

**Returns:** `dict` - Format names and extensions

### BackgroundRemover

Located in `src/features/image_tools/bg_remover.py`

#### Methods

##### `is_available()`

Check if background removal is available.

**Returns:** `bool` - True if rembg is installed

##### `remove_background(input_path, output_path, model_name='u2net')`

Remove background from image.

**Parameters:**
- `input_path` (str): Path to input image
- `output_path` (str): Path for output image
- `model_name` (str): AI model to use

**Returns:** `bool` - True if successful

##### `batch_remove_background(input_files, output_dir, model_name='u2net')`

Remove backgrounds from multiple images.

**Returns:** `dict` - Results with 'success' and 'failed' lists

##### `get_available_models()`

Get list of available AI models.

**Returns:** `list` - Model names

### AndroidIconGenerator

Located in `src/features/image_tools/icon_generator.py`

#### Methods

##### `generate_icon_set(input_path, output_dir, app_name='app')`

Generate complete Android icon set.

**Parameters:**
- `input_path` (str): Path to input PNG image
- `output_dir` (str): Base output directory
- `app_name` (str): App name for organization

**Returns:** `dict` - Results with generated files and errors

##### `get_icon_info()`

Get Android icon requirements information.

**Returns:** `dict` - Icon specifications and requirements

### PDFConverter

Located in `src/features/document_tools/pdf_converter.py`

#### Methods

##### `is_pdf2docx_available()`

Check if PDF to DOCX conversion is available.

**Returns:** `bool` - True if pdf2docx is installed

##### `is_pdf2xlsx_available()`

Check if PDF to XLSX conversion is available.

**Returns:** `bool` - True if pdfplumber and openpyxl are installed

##### `convert_pdf_to_docx(input_path, output_path, start_page=0, end_page=None)`

Convert PDF to DOCX.

**Parameters:**
- `input_path` (str): Path to input PDF
- `output_path` (str): Path for output DOCX
- `start_page` (int): Starting page (0-indexed)
- `end_page` (int, optional): Ending page

**Returns:** `bool` - True if successful

##### `convert_pdf_to_xlsx(input_path, output_path, extract_tables_only=True)`

Convert PDF to XLSX.

**Parameters:**
- `input_path` (str): Path to input PDF
- `output_path` (str): Path for output XLSX
- `extract_tables_only` (bool): Extract only tables vs all text

**Returns:** `bool` - True if successful

## Configuration

### Config Module

Located in `src/core/config.py`

#### Constants

- `APP_NAME`: Application name
- `APP_VERSION`: Version number
- `WINDOW_WIDTH/HEIGHT`: Default window dimensions
- `PRIMARY_COLOR/SECONDARY_COLOR`: Theme colors
- `SUPPORTED_IMAGE_FORMATS`: List of supported image extensions
- `MAX_FILE_SIZE_MB`: Maximum file size limit
- `ANDROID_ICON_SIZES`: Dictionary of Android density sizes

#### Paths

- `STORAGE_DIR`: Base storage directory
- `TEMP_DIR`: Temporary files directory
- `DATA_DIR`: Application data directory

## Error Handling

All methods return boolean success indicators or result dictionaries. Common error patterns:

```python
# Single operation
success = ImageResizer.resize_image(input_path, output_path)
if not success:
    print("Operation failed")

# Batch operation
results = ImageResizer.batch_resize(files, output_dir)
if results['failed']:
    print(f"Failed files: {results['failed']}")
```