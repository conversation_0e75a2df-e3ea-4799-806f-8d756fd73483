"""File handling utilities for HielToolbox."""

import os
import shutil
from pathlib import Path
from typing import List, Optional
from core.config import SUPPORTED_IMAGE_FORMATS, MAX_FILE_SIZE_MB, TEMP_DIR


def validate_file_size(file_path: str) -> bool:
    """Check if file size is within allowed limits."""
    try:
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        return size_mb <= MAX_FILE_SIZE_MB
    except OSError:
        return False


def validate_image_format(file_path: str) -> bool:
    """Check if file has a supported image format."""
    return Path(file_path).suffix.lower() in SUPPORTED_IMAGE_FORMATS


def get_safe_filename(filename: str, output_dir: str) -> str:
    """Generate a safe filename that doesn't conflict with existing files."""
    base_path = Path(output_dir) / filename
    if not base_path.exists():
        return str(base_path)

    name = base_path.stem
    suffix = base_path.suffix
    counter = 1

    while True:
        new_name = f"{name}_{counter}{suffix}"
        new_path = Path(output_dir) / new_name
        if not new_path.exists():
            return str(new_path)
        counter += 1


def create_temp_file(original_path: str, suffix: str = None) -> str:
    """Create a temporary file path based on original file."""
    original = Path(original_path)
    if suffix is None:
        suffix = original.suffix

    temp_name = f"{original.stem}_temp{suffix}"
    return str(TEMP_DIR / temp_name)


def cleanup_temp_files():
    """Clean up temporary files."""
    try:
        for file_path in TEMP_DIR.glob("*"):
            if file_path.is_file():
                file_path.unlink()
    except Exception as e:
        print(f"Error cleaning temp files: {e}")


def copy_to_temp(file_path: str) -> str:
    """Copy file to temp directory and return temp path."""
    temp_path = create_temp_file(file_path)
    shutil.copy2(file_path, temp_path)
    return temp_path